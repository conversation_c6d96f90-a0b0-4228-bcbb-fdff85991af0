# ODPS 表级别权限功能说明

## 功能概述

本项目已完整实现 ODPS (阿里云 MaxCompute) 表级别权限管理功能，支持细粒度的表级别权限控制。

## 功能特性

### ✅ 已实现功能
- **ODPS SQL 解析**: 支持解析 ODPS SQL 语句中的表引用
- **表级别权限验证**: 支持表级别的查询权限验证
- **权限申请流程**: 完整的权限申请和审批工作流
- **权限有效期管理**: 支持设置权限有效期
- **行数限制控制**: 支持设置查询结果行数限制
- **完整测试用例**: 包含完整的单元测试和集成测试

### 🔧 核心组件
1. **SQL 解析器** (`sql/query_privileges.py:_odps_table_ref`)
2. **权限验证器** (`sql/query_privileges.py:query_priv_check`)
3. **权限申请处理** (`sql/query_privileges.py:query_priv_apply`)
4. **ODPS 引擎** (`sql/engines/odps.py`)

## 使用说明

### 1. 配置 ODPS 实例

#### 通过管理后台配置
1. 访问管理后台: http://127.0.0.1:8000/admin/
2. 进入 "实例管理" -> "数据库实例"
3. 添加新实例，选择数据库类型为 "ODPS"
4. 填写连接信息:
   ```
   实例名称: test_odps_instance
   数据库类型: odps
   连接地址: https://service.eu-central-1.maxcompute.aliyun.com/api
   端口: 443
   用户名: <AccessKey ID>
   密码: <AccessKey Secret>
   数据库名: <项目名称>
   ```

#### 通过代码配置
```python
from sql.models import Instance

instance = Instance.objects.create(
    instance_name='test_odps_instance',
    db_type='odps',
    host='https://service.eu-central-1.maxcompute.aliyun.com/api',
    port=443,
    user='<AccessKey ID>',
    password='<AccessKey Secret>',
    db_name='<项目名称>',
    is_enable=True
)
```

### 2. 设置资源组和审批流程

#### 创建资源组
```python
from sql.models import ResourceGroup

resource_group = ResourceGroup.objects.create(
    group_id=1,
    group_name='test_resource_group',
    ding_webhook='',
    is_enable=True
)

# 关联实例到资源组
resource_group.instances.add(instance)
```

#### 配置审批流程
```python
from sql.models import WorkflowAuditSetting, WorkflowType
from django.contrib.auth.models import Group

# 创建审批权限组
auth_group = Group.objects.create(name='test_group')

# 配置查询权限申请的审批流程
audit_setting = WorkflowAuditSetting.objects.create(
    group_id=resource_group.group_id,
    group_name=resource_group.group_name,
    workflow_type=WorkflowType.QUERY,  # 查询权限申请
    audit_auth_groups=str(auth_group.id)
)
```

### 3. 用户权限申请

#### 申请表级别权限
1. 访问权限申请页面: http://127.0.0.1:8000/queryapplylist/
2. 点击 "申请权限"
3. 填写申请信息:
   - 申请标题: 申请 dev_dlf.test_table 查询权限
   - 资源组: test_resource_group
   - 实例: test_odps_instance
   - 权限类型: 表权限
   - 数据库: dev_dlf
   - 表名: test_table
   - 有效期: 2025-12-31
   - 行数限制: 1000

#### 通过代码申请权限
```python
from sql.models import QueryPrivileges, Instance, Users
from datetime import datetime, timedelta

user = Users.objects.get(username='test_user')
instance = Instance.objects.get(instance_name='test_odps_instance')

# 创建表级别权限
privilege = QueryPrivileges.objects.create(
    user_name=user.username,
    instance=instance,
    db_name='dev_dlf',
    table_name='test_table',
    valid_date=datetime.now() + timedelta(days=365),
    limit_num=1000,
    priv_type=2  # 2 表示表权限
)
```

### 4. 权限验证

#### SQL 查询权限验证
```python
from sql.query_privileges import query_priv_check

# 验证用户查询权限
result = query_priv_check(
    user=user,
    instance=instance,
    db_name='dev_dlf',
    sql_content='SELECT * FROM dev_dlf.test_table LIMIT 10',
    limit_num=100
)

if result['status'] == 0:
    print("权限验证通过")
    print(f"查询行数限制: {result['data']['limit_num']}")
else:
    print(f"权限验证失败: {result['msg']}")
```

## 测试功能

### 运行测试脚本

#### 1. 设置测试环境
```bash
python setup_odps_test.py
```
这个脚本会自动创建:
- ODPS 实例配置
- 测试用户和权限组
- 资源组配置
- 审批流程配置
- 示例权限记录

#### 2. 运行权限功能测试
```bash
python test_odps_permissions.py
```
测试内容包括:
- ODPS SQL 解析功能
- 表级别权限验证
- 权限检查逻辑

#### 3. 运行简化测试
```bash
python simple_odps_test.py
```
快速验证基础功能是否正常。

### 单元测试
```bash
# 运行 ODPS 权限相关测试
python manage.py test sql.test_odps_table_privileges -v 2

# 运行所有权限测试
python manage.py test sql.test_query_privileges -v 2
```

## 支持的 SQL 语句

### 查询语句
```sql
-- 简单查询
SELECT * FROM dev_dlf.test_table;

-- 带条件查询
SELECT id, name FROM dev_dlf.test_table WHERE id > 100;

-- 多表关联
SELECT a.*, b.name 
FROM dev_dlf.table1 a 
JOIN dev_dlf.table2 b ON a.id = b.id;

-- 跨项目查询
SELECT * FROM another_project.table1;
```

### 权限验证逻辑
1. **解析 SQL**: 提取所有涉及的表
2. **检查权限**: 验证用户对每个表的访问权限
3. **应用限制**: 根据权限设置应用行数限制
4. **返回结果**: 返回验证结果和限制信息

## 权限类型说明

### 库权限 (priv_type=1)
- 用户拥有整个数据库的查询权限
- 可以查询该库下的所有表
- 优先级高于表权限

### 表权限 (priv_type=2)
- 用户只能查询指定的表
- 需要为每个表单独申请权限
- 支持细粒度控制

## 配置参数

### 系统配置
```python
# settings.py 或 local_settings.py

# 管理员查询限制 (超级用户默认行数限制)
ADMIN_QUERY_LIMIT = 5000

# 是否禁止自审 (用户不能审批自己的申请)
BAN_SELF_AUDIT = True

# ODPS 连接超时设置
ODPS_CONNECTION_TIMEOUT = 30
```

### 实例标签
为支持查询的实例添加 `can_read` 标签:
```python
from sql.models import InstanceTag

can_read_tag = InstanceTag.objects.get_or_create(
    tag_code='can_read',
    defaults={'tag_name': '支持查询', 'active': True}
)[0]

instance.instance_tag.add(can_read_tag)
```

## 故障排除

### 常见问题

#### 1. 权限申请被拒绝
**错误**: "你已拥有test_odps_instance实例dev_dlf库的全部权限，不能重复申请"

**原因**: 用户已有库权限或是超级用户

**解决**: 
- 检查用户是否已有库权限
- 超级用户无需申请权限，可直接查询

#### 2. SQL 解析失败
**错误**: "sequence item 0: expected str instance, dict found"

**原因**: SQL 解析器返回格式问题

**解决**: 检查 `_odps_table_ref` 函数的返回格式

#### 3. 连接 ODPS 失败
**错误**: "ODPS连接失败"

**原因**: AccessKey 或网络配置问题

**解决**:
- 验证 AccessKey ID 和 Secret
- 检查网络连接和防火墙设置
- 确认项目名称正确

### 调试方法

#### 启用详细日志
```python
# settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': 'logs/archery.log',
        },
    },
    'loggers': {
        'sql.query_privileges': {
            'handlers': ['file'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'sql.engines.odps': {
            'handlers': ['file'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}
```

#### 使用 Django Shell 调试
```bash
python manage.py shell

# 测试权限验证
from sql.query_privileges import query_priv_check, _odps_table_ref
from sql.models import Users, Instance

user = Users.objects.get(username='test_user')
instance = Instance.objects.get(instance_name='test_odps_instance')

# 测试 SQL 解析
tables = _odps_table_ref('SELECT * FROM dev_dlf.test_table', instance, 'dev_dlf')
print(tables)

# 测试权限检查
result = query_priv_check(user, instance, 'dev_dlf', 'SELECT * FROM dev_dlf.test_table', 100)
print(result)
```

## 扩展开发

### 添加新的 SQL 语句支持
修改 `sql/query_privileges.py` 中的 `_odps_table_ref` 函数:

```python
def _odps_table_ref(sql_content, instance, db_name):
    """
    解析ODPS SQL语句中的表引用
    """
    # 添加新的 SQL 语句解析逻辑
    pass
```

### 自定义权限验证逻辑
扩展 `query_priv_check` 函数:

```python
def custom_priv_check(user, instance, db_name, sql_content, limit_num):
    """
    自定义权限验证逻辑
    """
    # 添加自定义验证规则
    pass
```

## 相关文档

- [Archery 官方文档](https://archerydms.com/)
- [ODPS Python SDK](https://pyodps.readthedocs.io/)
- [Django 权限系统](https://docs.djangoproject.com/en/4.1/topics/auth/)
- [项目 GitHub](https://github.com/hhyo/Archery)
