#!/bin/bash

# Archery 启动脚本
# 使用方法: ./start.sh

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 启动 Archery 项目...${NC}"

# 检查虚拟环境
if [[ ! -d ".venv" ]]; then
    echo "❌ 未找到虚拟环境，请先运行安装脚本"
    echo "   ./install.sh"
    exit 1
fi

# 检查 manage.py
if [[ ! -f "manage.py" ]]; then
    echo "❌ 未找到 manage.py，请确保在项目根目录运行"
    exit 1
fi

echo -e "${GREEN}✅ 激活虚拟环境并启动开发服务器...${NC}"

# 激活虚拟环境并启动服务
source .venv/bin/activate && python manage.py runserver 0.0.0.0:8000
