#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
ODPS表级别权限测试环境设置脚本
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archery.settings')
django.setup()

from sql.models import Instance, ResourceGroup, QueryPrivileges, Users
from django.contrib.auth.models import Group


def create_test_odps_instance():
    """创建测试用的ODPS实例"""
    instance_name = 'test_odps_instance'
    
    # 检查实例是否已存在
    if Instance.objects.filter(instance_name=instance_name).exists():
        print(f"✅ ODPS实例 '{instance_name}' 已存在")
        return Instance.objects.get(instance_name=instance_name)
    
    # 创建ODPS实例
    instance = Instance.objects.create(
        instance_name=instance_name,
        db_type='odps',
        type='master',
        host='https://service.eu-central-1.maxcompute.aliyun.com/api',
        port=0,
        user='your_access_key_id',  # 需要替换为真实的AccessKey ID
        password='your_access_key_secret',  # 需要替换为真实的AccessKey Secret
        db_name='dev_dlf',
        charset='utf8'
    )
    
    print(f"✅ 成功创建ODPS实例: {instance_name}")
    return instance


def create_test_user_and_group():
    """创建测试用户和组"""
    # 创建测试用户
    username = 'test_user'
    if not Users.objects.filter(username=username).exists():
        user = Users.objects.create_user(
            username=username,
            email='<EMAIL>',
            password='test123456'
        )
        print(f"✅ 成功创建测试用户: {username}")
    else:
        user = Users.objects.get(username=username)
        print(f"✅ 测试用户 '{username}' 已存在")
    
    # 创建测试组
    group_name = 'test_group'
    group, created = Group.objects.get_or_create(name=group_name)
    if created:
        print(f"✅ 成功创建测试组: {group_name}")
    else:
        print(f"✅ 测试组 '{group_name}' 已存在")
    
    # 将用户添加到组
    user.groups.add(group)
    
    return user, group


def create_test_resource_group(instance):
    """创建测试资源组"""
    group_name = 'test_resource_group'

    if not ResourceGroup.objects.filter(group_name=group_name).exists():
        resource_group = ResourceGroup.objects.create(
            group_name=group_name,
            group_parent_id=0,
            group_sort=1,
            group_level=1,
            is_deleted=0
        )

        # 将实例添加到资源组
        instance.resource_group.add(resource_group)

        print(f"✅ 成功创建资源组: {group_name}")
        return resource_group
    else:
        resource_group = ResourceGroup.objects.get(group_name=group_name)
        # 确保实例在资源组中
        instance.resource_group.add(resource_group)
        print(f"✅ 资源组 '{group_name}' 已存在")
        return resource_group


def create_test_table_privilege(instance, user):
    """创建测试表级别权限"""
    # 检查是否已存在相同权限
    existing_privilege = QueryPrivileges.objects.filter(
        user_name=user.username,
        instance=instance,
        db_name='dev_dlf',
        table_name='test_table',
        priv_type=2  # 表级别权限
    ).first()
    
    if existing_privilege:
        print(f"✅ 表级别权限已存在: {user.username} -> dev_dlf.test_table")
        return existing_privilege
    
    # 创建表级别权限
    privilege = QueryPrivileges.objects.create(
        user_name=user.username,
        instance=instance,
        db_name='dev_dlf',
        table_name='test_table',
        priv_type=2,  # 表级别权限
        valid_date='2025-12-31',
        limit_num=1000,
        is_deleted=0
    )
    
    print(f"✅ 成功创建表级别权限: {user.username} -> dev_dlf.test_table")
    return privilege


def main():
    """主函数"""
    print("🚀 开始设置ODPS表级别权限测试环境...")
    print()
    
    try:
        # 1. 创建ODPS实例
        print("1. 创建ODPS实例...")
        instance = create_test_odps_instance()
        print()
        
        # 2. 创建测试用户和组
        print("2. 创建测试用户和组...")
        user, group = create_test_user_and_group()
        print()
        
        # 3. 创建资源组
        print("3. 创建资源组...")
        resource_group = create_test_resource_group(instance)
        print()
        
        # 4. 创建表级别权限
        print("4. 创建表级别权限...")
        privilege = create_test_table_privilege(instance, user)
        print()
        
        print("🎉 ODPS测试环境设置完成！")
        print()
        print("📋 测试信息:")
        print(f"   - ODPS实例: {instance.instance_name}")
        print(f"   - 项目名: {instance.db_name}")
        print(f"   - 测试用户: {user.username}")
        print(f"   - 测试密码: test123456")
        print(f"   - 资源组: {resource_group.group_name}")
        print()
        print("🔗 测试链接:")
        print("   - 权限申请页面: http://127.0.0.1:8000/queryapplylist/")
        print("   - 实例管理页面: http://127.0.0.1:8000/instance/")
        print("   - 管理后台: http://127.0.0.1:8000/admin/")
        print()
        print("⚠️  注意: 请在实例配置中填入真实的ODPS AccessKey信息")
        
    except Exception as e:
        print(f"❌ 设置过程中出现错误: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
