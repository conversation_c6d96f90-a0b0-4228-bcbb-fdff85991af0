# -*- coding: utf-8 -*-
import datetime
from unittest.mock import patch, MagicMock

from django.test import TestCase, Client
from django.contrib.auth.models import Permission
from django.urls import reverse

from sql.models import Instance, QueryPrivileges, Users
from sql.instance import get_user_authorized_tables


class TestInstanceTablePermissions(TestCase):
    """测试实例表权限功能"""

    def setUp(self):
        """设置测试数据"""
        self.client = Client()
        
        # 创建测试用户
        self.user = Users.objects.create_user(
            username='test_user',
            password='test_password',
            email='<EMAIL>'
        )

        # 创建超级管理员用户
        self.superuser = Users.objects.create_superuser(
            username='admin',
            password='admin_password',
            email='<EMAIL>'
        )
        
        # 创建测试实例
        self.instance = Instance.objects.create(
            instance_name='test_instance',
            type='slave',
            db_type='mysql',
            host='127.0.0.1',
            port=3306,
            user='test',
            password='test'
        )

    def test_get_user_authorized_tables_superuser(self):
        """测试超级管理员获取表列表"""
        with patch('sql.instance.get_engine') as mock_get_engine:
            # 模拟数据库引擎返回的表列表
            mock_engine = MagicMock()
            mock_result = MagicMock()
            mock_result.rows = ['table1', 'table2', 'table3']
            mock_result.error = None
            mock_engine.get_all_tables.return_value = mock_result
            mock_get_engine.return_value = mock_engine
            
            # 测试超级管理员
            tables = get_user_authorized_tables(
                user=self.superuser,
                instance=self.instance,
                db_name='test_db'
            )
            
            self.assertEqual(tables, ['table1', 'table2', 'table3'])

    def test_get_user_authorized_tables_with_db_permission(self):
        """测试有数据库权限的用户获取表列表"""
        # 创建数据库权限
        QueryPrivileges.objects.create(
            user_name=self.user.username,
            user_display=self.user.get_full_name() or self.user.username,
            instance=self.instance,
            db_name='test_db',
            table_name='',
            valid_date=datetime.date.today() + datetime.timedelta(days=30),
            limit_num=1000,
            priv_type=1  # 数据库权限
        )
        
        with patch('sql.instance.get_engine') as mock_get_engine:
            # 模拟数据库引擎返回的表列表
            mock_engine = MagicMock()
            mock_result = MagicMock()
            mock_result.rows = ['table1', 'table2', 'table3']
            mock_result.error = None
            mock_engine.get_all_tables.return_value = mock_result
            mock_get_engine.return_value = mock_engine
            
            tables = get_user_authorized_tables(
                user=self.user,
                instance=self.instance,
                db_name='test_db'
            )
            
            self.assertEqual(tables, ['table1', 'table2', 'table3'])

    def test_get_user_authorized_tables_with_table_permissions(self):
        """测试只有表权限的用户获取表列表"""
        # 创建表权限
        QueryPrivileges.objects.create(
            user_name=self.user.username,
            user_display=self.user.get_full_name() or self.user.username,
            instance=self.instance,
            db_name='test_db',
            table_name='authorized_table1',
            valid_date=datetime.date.today() + datetime.timedelta(days=30),
            limit_num=500,
            priv_type=2  # 表权限
        )
        
        QueryPrivileges.objects.create(
            user_name=self.user.username,
            user_display=self.user.get_full_name() or self.user.username,
            instance=self.instance,
            db_name='test_db',
            table_name='authorized_table2',
            valid_date=datetime.date.today() + datetime.timedelta(days=30),
            limit_num=500,
            priv_type=2  # 表权限
        )
        
        tables = get_user_authorized_tables(
            user=self.user,
            instance=self.instance,
            db_name='test_db'
        )
        
        self.assertIn('authorized_table1', tables)
        self.assertIn('authorized_table2', tables)
        self.assertEqual(len(tables), 2)

    def test_get_user_authorized_tables_no_permissions(self):
        """测试没有权限的用户获取表列表"""
        tables = get_user_authorized_tables(
            user=self.user,
            instance=self.instance,
            db_name='test_db'
        )
        
        self.assertEqual(tables, [])

    def test_instance_resource_table_with_permissions(self):
        """测试通过API获取有权限的表列表"""
        # 创建表权限
        QueryPrivileges.objects.create(
            user_name=self.user.username,
            user_display=self.user.get_full_name() or self.user.username,
            instance=self.instance,
            db_name='test_db',
            table_name='authorized_table',
            valid_date=datetime.date.today() + datetime.timedelta(days=30),
            limit_num=500,
            priv_type=2  # 表权限
        )
        
        # 登录用户
        self.client.force_login(self.user)
        
        # 请求表列表
        response = self.client.get('/instance/instance_resource/', {
            'instance_name': 'test_instance',
            'db_name': 'test_db',
            'resource_type': 'table'
        })
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['status'], 0)
        self.assertIn('authorized_table', data['data'])
