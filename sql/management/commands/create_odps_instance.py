# -*- coding: UTF-8 -*-
"""
Django管理命令：创建ODPS实例
"""
from django.core.management.base import BaseCommand
from sql.models import Instance, ResourceGroup


class Command(BaseCommand):
    help = '创建ODPS测试实例'

    def add_arguments(self, parser):
        parser.add_argument('--name', type=str, default='test_odps_instance', help='实例名称')
        parser.add_argument('--host', type=str, required=True, help='ODPS服务地址')
        parser.add_argument('--user', type=str, required=True, help='AccessKey ID')
        parser.add_argument('--password', type=str, required=True, help='AccessKey Secret')
        parser.add_argument('--project', type=str, required=True, help='ODPS项目名')

    def handle(self, *args, **options):
        instance_name = options['name']
        host = options['host']
        user = options['user']
        password = options['password']
        project = options['project']

        # 检查实例是否已存在
        if Instance.objects.filter(instance_name=instance_name).exists():
            self.stdout.write(
                self.style.WARNING(f'实例 {instance_name} 已存在')
            )
            return

        # 创建ODPS实例
        instance = Instance.objects.create(
            instance_name=instance_name,
            db_type='odps',
            type='master',
            host=host,
            port=0,
            user=user,
            password=password,
            db_name=project,
            charset='utf8'
        )

        self.stdout.write(
            self.style.SUCCESS(f'成功创建ODPS实例: {instance_name}')
        )
        self.stdout.write(f'实例ID: {instance.id}')
        self.stdout.write(f'项目名: {project}')
        self.stdout.write(f'服务地址: {host}')
