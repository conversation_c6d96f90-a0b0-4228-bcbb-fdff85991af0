# -*- coding: UTF-8 -*-
"""
测试ODPS表级别权限功能
"""
import unittest
from unittest.mock import Mock, patch
from django.test import TestCase
from django.contrib.auth.models import User
from sql.models import Instance, QueryPrivileges, QueryPrivilegesApply, ResourceGroup
from sql.query_privileges import query_priv_check, _odps_table_ref, _db_priv, _tb_priv
import datetime


class ODPSTablePrivilegesTest(TestCase):
    def setUp(self):
        """设置测试数据"""
        # 创建测试用户
        self.user = User.objects.create_user(
            username='test_user',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # 创建ODPS实例
        self.odps_instance = Instance.objects.create(
            instance_name='test_odps',
            db_type='odps',
            host='http://service.odps.aliyun.com/api',
            port=80,
            user='test_user',
            password='test_password',
            db_name='test_project'
        )
        
        # 创建资源组
        self.resource_group = ResourceGroup.objects.create(
            group_name='test_group'
        )

    def test_odps_table_ref_parsing(self):
        """测试ODPS SQL解析功能"""
        # 测试简单的SELECT语句
        sql = "SELECT * FROM test_table"
        table_ref = _odps_table_ref(sql, self.odps_instance, 'test_project')
        
        self.assertEqual(len(table_ref), 1)
        self.assertEqual(table_ref[0]['schema'], 'test_project')
        self.assertEqual(table_ref[0]['name'], 'test_table')
        
        # 测试带schema的SELECT语句
        sql = "SELECT * FROM project1.table1 JOIN project2.table2 ON project1.table1.id = project2.table2.id"
        table_ref = _odps_table_ref(sql, self.odps_instance, 'test_project')
        
        self.assertEqual(len(table_ref), 2)
        # 验证表引用包含正确的schema和表名
        schemas = [ref['schema'] for ref in table_ref]
        names = [ref['name'] for ref in table_ref]
        self.assertIn('project1', schemas)
        self.assertIn('project2', schemas)
        self.assertIn('table1', names)
        self.assertIn('table2', names)

    def test_db_priv_check(self):
        """测试数据库权限检查"""
        # 创建数据库权限
        QueryPrivileges.objects.create(
            user_name=self.user.username,
            user_display=self.user.get_full_name() or self.user.username,
            instance=self.odps_instance,
            db_name='test_project',
            table_name='',
            valid_date=datetime.date.today() + datetime.timedelta(days=30),
            limit_num=1000,
            priv_type=1  # DATABASE权限
        )
        
        # 测试权限检查
        result = _db_priv(self.user, self.odps_instance, 'test_project')
        self.assertEqual(result, 1000)
        
        # 测试无权限的情况
        result = _db_priv(self.user, self.odps_instance, 'other_project')
        self.assertFalse(result)

    def test_tb_priv_check(self):
        """测试表权限检查"""
        # 创建表权限
        QueryPrivileges.objects.create(
            user_name=self.user.username,
            user_display=self.user.get_full_name() or self.user.username,
            instance=self.odps_instance,
            db_name='test_project',
            table_name='test_table',
            valid_date=datetime.date.today() + datetime.timedelta(days=30),
            limit_num=500,
            priv_type=2  # TABLE权限
        )
        
        # 测试权限检查
        result = _tb_priv(self.user, self.odps_instance, 'test_project', 'test_table')
        self.assertEqual(result, 500)
        
        # 测试无权限的情况
        result = _tb_priv(self.user, self.odps_instance, 'test_project', 'other_table')
        self.assertFalse(result)

    @patch('sql.query_privileges._odps_table_ref')
    def test_query_priv_check_with_table_permission(self, mock_table_ref):
        """测试ODPS查询权限检查（有表权限）"""
        # 模拟SQL解析结果
        mock_table_ref.return_value = [
            {'schema': 'test_project', 'name': 'test_table'}
        ]
        
        # 创建表权限
        QueryPrivileges.objects.create(
            user_name=self.user.username,
            user_display=self.user.get_full_name() or self.user.username,
            instance=self.odps_instance,
            db_name='test_project',
            table_name='test_table',
            valid_date=datetime.date.today() + datetime.timedelta(days=30),
            limit_num=500,
            priv_type=2  # TABLE权限
        )
        
        # 测试权限检查
        result = query_priv_check(
            self.user, 
            self.odps_instance, 
            'test_project', 
            'SELECT * FROM test_table', 
            1000
        )
        
        self.assertEqual(result['status'], 0)
        self.assertEqual(result['data']['limit_num'], 500)

    @patch('sql.query_privileges._odps_table_ref')
    def test_query_priv_check_without_permission(self, mock_table_ref):
        """测试ODPS查询权限检查（无权限）"""
        # 模拟SQL解析结果
        mock_table_ref.return_value = [
            {'schema': 'test_project', 'name': 'test_table'}
        ]
        
        # 测试无权限的情况
        result = query_priv_check(
            self.user, 
            self.odps_instance, 
            'test_project', 
            'SELECT * FROM test_table', 
            1000
        )
        
        self.assertEqual(result['status'], 2)
        self.assertIn('你无test_project.test_table表的查询权限', result['msg'])


if __name__ == '__main__':
    unittest.main()
