# PyMySQL 升级指南：解决 `module 'pymysql' has no attribute 'escape_string'` 错误

## 问题描述

在升级 PyMySQL 到 1.4.6 版本后，`/instance/instance_resource/` 接口报错：
```
module 'pymysql' has no attribute 'escape_string'
```

## 问题原因

在 PyMySQL 1.4.x 版本中，`pymysql.escape_string()` 函数被移除了。这个函数在早期版本（0.9.3）中是模块级别的函数，但在新版本中已经不再提供。

## 解决方案

### 1. 修改的文件

#### `sql/engines/mysql.py`
- 更新了 `escape_string` 方法，增加了向后兼容性
- 首先尝试使用旧版本的 `pymysql.escape_string()`
- 如果失败，则使用手动转义方法

#### `sql/engines/goinception.py`
- 同样更新了 `escape_string` 方法
- 实现了相同的向后兼容逻辑

#### `requirements.txt`
- 将 `pymysql==0.9.3` 升级到 `pymysql==1.4.6`

### 2. 技术实现细节

新的 `escape_string` 方法实现了以下逻辑：

1. **空值处理**：如果输入为 `None`，返回 `"NULL"`
2. **类型转换**：确保输入是字符串类型
3. **向后兼容**：
   - 首先尝试使用 `pymysql.escape_string()`（旧版本兼容）
   - 如果方法不存在，使用手动转义
4. **手动转义规则**：
   - `\0` → `\\0`
   - `\n` → `\\n`
   - `\r` → `\\r`
   - `\032` → `\\Z`
   - `"` → `\\"`
   - `'` → `\\'`
   - `\` → `\\\\`

## 代码优化建议

### 1. 使用参数化查询

**推荐做法**：尽可能使用参数化查询而不是字符串拼接

```python
# 不推荐：字符串拼接
sql = f"SELECT * FROM table WHERE name = '{self.escape_string(name)}'"

# 推荐：参数化查询
sql = "SELECT * FROM table WHERE name = %s"
result = self.query(sql=sql, parameters=(name,))
```

### 2. 统一转义处理

在 `EngineBase` 基类中提供默认的转义实现：

```python
def escape_string(self, value: str) -> str:
    """默认的字符串转义实现"""
    if value is None:
        return "NULL"
    if not isinstance(value, str):
        value = str(value)
    
    # 基本的SQL注入防护
    escape_dict = {
        '\0': '\\0',
        '\n': '\\n',
        '\r': '\\r',
        '\032': '\\Z',
        '"': '\\"',
        "'": "\\'",
        '\\': '\\\\'
    }
    return ''.join(escape_dict.get(c, c) for c in value)
```

### 3. 连接池优化

考虑使用连接池来提高数据库连接的效率：

```python
from pymysql.connections import Connection
from pymysql.cursors import DictCursor

class ConnectionPool:
    def __init__(self, **kwargs):
        self.connection_kwargs = kwargs
        self._pool = []
    
    def get_connection(self):
        # 连接池实现
        pass
```

### 4. 错误处理改进

增强错误处理和日志记录：

```python
def escape_string(self, value: str) -> str:
    """改进的字符串转义方法"""
    try:
        # 转义逻辑
        return escaped_value
    except Exception as e:
        logger.error(f"字符串转义失败: {value}, 错误: {e}")
        # 返回安全的默认值或抛出异常
        raise
```

## 测试验证

使用提供的测试脚本验证修改：

```bash
python test_pymysql_escape.py
```

测试脚本会验证：
1. PyMySQL 版本信息
2. MySQL 引擎的 escape_string 方法
3. GoInception 引擎的 escape_string 方法
4. 各种边界情况的处理

## 部署步骤

1. **备份当前环境**
2. **更新代码**：应用上述文件修改
3. **升级依赖**：
   ```bash
   pip install pymysql==1.4.6
   ```
4. **运行测试**：
   ```bash
   python test_pymysql_escape.py
   ```
5. **重启应用**
6. **验证功能**：测试 `/instance/instance_resource/` 接口

## 注意事项

1. **向后兼容性**：修改后的代码同时支持旧版本和新版本的 PyMySQL
2. **性能影响**：手动转义的性能略低于原生方法，但差异很小
3. **安全性**：手动转义实现了基本的 SQL 注入防护
4. **监控**：建议在生产环境中监控相关接口的性能和错误率

## 长期优化建议

1. **迁移到 ORM**：考虑更多使用 Django ORM 而不是原生 SQL
2. **统一数据库访问层**：创建统一的数据库访问接口
3. **连接管理优化**：实现更好的连接池和连接管理
4. **安全审计**：定期审计 SQL 查询的安全性

通过这些修改，系统可以成功升级到 PyMySQL 1.4.6，同时保持向后兼容性和安全性。
