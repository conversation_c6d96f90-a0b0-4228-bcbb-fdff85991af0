# 在线查询表权限过滤功能改进

## 问题描述

在申请完对应表的权限并审核通过后，在线查询页面仍然显示所有表，而不是只显示已经授权的表。这样的用户体验不够友好，用户可能会尝试查询没有权限的表，导致权限错误。

## 解决方案

### 1. 核心修改

在 `sql/instance.py` 文件中：

1. **新增函数 `get_user_authorized_tables`**：
   - 根据用户权限获取有权限的表列表
   - 支持超级管理员（返回所有表）
   - 支持数据库级权限（返回所有表）
   - 支持表级权限（只返回有权限的表）

2. **修改 `instance_resource` 函数**：
   - 当 `resource_type="table"` 时，调用新的权限过滤函数
   - 移除了缓存装饰器，因为不同用户的结果不同

### 2. 权限逻辑

```python
def get_user_authorized_tables(user, instance, db_name, schema_name=""):
    """
    获取用户有权限的表列表
    """
    # 1. 超级管理员或有查询所有实例权限 -> 返回所有表
    if user.is_superuser or user.has_perm("sql.query_all_instances"):
        return all_tables
    
    # 2. 有数据库权限 -> 返回所有表
    if _db_priv(user, instance, db_name):
        return all_tables
    
    # 3. 只有表权限 -> 返回有权限的表
    return user_authorized_tables
```

### 3. 测试验证

创建了完整的测试用例 `sql/test_instance_table_permissions.py`：

- ✅ 测试超级管理员获取所有表
- ✅ 测试有数据库权限的用户获取所有表  
- ✅ 测试只有表权限的用户获取授权表
- ✅ 测试无权限用户获取空列表
- ✅ 测试API接口正确返回

### 4. 功能效果

#### 修改前：
- 用户在在线查询页面看到所有表
- 用户可能尝试查询无权限的表，导致错误
- 用户体验不佳

#### 修改后：
- 普通用户只看到有权限的表
- 管理员仍然看到所有表
- 避免用户尝试查询无权限的表
- 提升用户体验

### 5. 演示示例

运行 `demo_table_permissions.py` 可以看到：

```
假设数据库 'demo_db' 中有以下所有表:
  users, orders, products, logs, configs, reports

1. 普通用户 'demo_user' 的权限:
   有权限的表: ['users', 'orders', 'products']
   说明: 用户只能看到已授权的表，而不是所有表

2. 管理员用户 'demo_admin' 的权限:
   有权限的表: 所有表 (管理员拥有全部权限)
   说明: 管理员可以看到所有表

3. 无权限用户 'no_perm_user' 的权限:
   有权限的表: []
   说明: 没有任何权限的用户看不到任何表
```

## 技术细节

### 1. 权限检查机制

- 使用现有的 `_db_priv()` 函数检查数据库权限
- 查询 `QueryPrivileges` 模型获取表级权限
- 支持权限有效期检查

### 2. 缓存策略调整

- 移除了原有的 `@cache_page` 装饰器
- 因为不同用户的结果不同，需要避免缓存混乱
- 如需缓存，应该基于用户ID创建不同的缓存键

### 3. 兼容性

- 保持了原有API接口不变
- 对管理员用户行为无影响
- 向后兼容现有功能

## 部署说明

1. 确保所有测试通过：
   ```bash
   python manage.py test sql.test_instance_table_permissions
   ```

2. 重启应用服务器以应用更改

3. 验证功能：
   - 使用有表权限的用户登录
   - 进入在线查询页面
   - 确认只显示有权限的表

## 注意事项

1. **性能考虑**：对于有大量表权限的用户，查询可能会稍慢
2. **缓存策略**：如需要缓存，建议基于用户ID实现
3. **权限同步**：确保权限变更后用户能及时看到更新

## 相关文件

- `sql/instance.py` - 核心逻辑修改
- `sql/test_instance_table_permissions.py` - 测试用例
- `demo_table_permissions.py` - 功能演示
- `TABLE_PERMISSIONS_IMPROVEMENT.md` - 本文档
