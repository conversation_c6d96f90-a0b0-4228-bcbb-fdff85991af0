# Archery 开发环境配置

## 快速开始

### 环境要求
- Python 3.10+
- uv (Python包管理工具)
- Git

### 一键安装
```bash
# 克隆项目
git clone https://github.com/hhyo/Archery.git
cd Archery

# 运行安装脚本
./install.sh

# 启动项目
./start.sh
```

### 手动安装
```bash
# 1. 安装 uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 2. 创建虚拟环境
uv venv --python 3.10 .venv
source .venv/bin/activate

# 3. 安装依赖
uv pip install -r requirements.txt

# 4. 初始化数据库
python manage.py migrate
python manage.py createsuperuser

# 5. 启动服务
source .venv/bin/activate && python manage.py runserver 0.0.0.0:8000
```

## 项目结构

```
Archery/
├── archery/                # Django 项目配置
│   ├── settings.py        # 主配置文件
│   ├── urls.py           # URL 路由
│   └── wsgi.py           # WSGI 配置
├── sql/                   # 核心应用
│   ├── models.py         # 数据模型
│   ├── views.py          # 视图函数
│   ├── query_privileges.py # 权限管理
│   └── engines/          # 数据库引擎
├── common/               # 公共模块
├── sql_api/             # API 接口
├── requirements.txt     # Python 依赖
├── manage.py           # Django 管理脚本
├── install.sh          # 一键安装脚本
├── start.sh            # 启动脚本
├── INSTALL.md          # 安装文档
└── README.md           # 项目说明
```

## 开发工具配置

### VS Code 配置
创建 `.vscode/settings.json`:
```json
{
    "python.defaultInterpreterPath": "./.venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true,
    "python.testing.unittestEnabled": false,
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true
    }
}
```

### PyCharm 配置
1. 打开项目
2. 设置 Python 解释器: `.venv/bin/python`
3. 配置运行配置:
   - Script path: `manage.py`
   - Parameters: `runserver 0.0.0.0:8000`
   - Working directory: 项目根目录

## 数据库配置

### SQLite (默认，开发环境)
```python
# settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}
```

### MySQL
```python
# local_settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'archery',
        'USER': 'archery',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
        },
    }
}
```

### PostgreSQL
```python
# local_settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'archery',
        'USER': 'archery',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}
```

## 测试

### 运行所有测试
```bash
python manage.py test
```

### 运行特定测试
```bash
# 测试 ODPS 权限功能
python manage.py test sql.test_odps_table_privileges

# 测试权限模块
python manage.py test sql.test_query_privileges
```

### ODPS 功能测试
```bash
# 设置测试环境
python setup_odps_test.py

# 运行权限测试
python test_odps_permissions.py

# 简化测试
python simple_odps_test.py
```

## 调试

### Django Debug Toolbar
```bash
# 安装
uv pip install django-debug-toolbar

# 在 settings.py 中添加配置
INSTALLED_APPS = [
    # ...
    'debug_toolbar',
]

MIDDLEWARE = [
    # ...
    'debug_toolbar.middleware.DebugToolbarMiddleware',
]

INTERNAL_IPS = [
    '127.0.0.1',
]
```

### 日志配置
```python
# settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': 'logs/archery.log',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'sql': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}
```

## 代码规范

### 代码格式化
```bash
# 安装 black
uv pip install black

# 格式化代码
black .

# 检查格式
black --check .
```

### 代码检查
```bash
# 安装 flake8
uv pip install flake8

# 检查代码
flake8 .
```

### 类型检查
```bash
# 安装 mypy
uv pip install mypy

# 类型检查
mypy .
```

## 常用命令

### Django 管理命令
```bash
# 创建迁移文件
python manage.py makemigrations

# 应用迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 收集静态文件
python manage.py collectstatic

# 启动开发服务器
python manage.py runserver 0.0.0.0:8000

# 进入 Django Shell
python manage.py shell

# 清空数据库
python manage.py flush
```

### 虚拟环境管理
```bash
# 激活虚拟环境
source .venv/bin/activate

# 退出虚拟环境
deactivate

# 查看已安装包
uv pip list

# 安装新包
uv pip install package_name

# 生成依赖文件
uv pip freeze > requirements.txt
```

## 部署

### 开发环境
```bash
# 启动开发服务器
python manage.py runserver 0.0.0.0:8000
```

### 生产环境
```bash
# 使用 Gunicorn
uv pip install gunicorn
gunicorn --bind 0.0.0.0:8000 --workers 4 archery.wsgi:application

# 使用 uWSGI
uv pip install uwsgi
uwsgi --http :8000 --module archery.wsgi
```

## 故障排除

### 常见问题

#### 依赖安装失败
```bash
# 清理缓存
uv cache clean

# 重新安装
rm -rf .venv
uv venv --python 3.10 .venv
source .venv/bin/activate
uv pip install -r requirements.txt
```

#### 数据库连接问题
```bash
# 检查数据库配置
python manage.py dbshell

# 重置数据库
python manage.py flush
python manage.py migrate
```

#### 端口占用
```bash
# 查看端口占用
lsof -i :8000

# 使用其他端口
python manage.py runserver 0.0.0.0:8001
```

### 获取帮助
- 项目文档: https://archerydms.com/
- GitHub Issues: https://github.com/hhyo/archery/issues
- 社区讨论: https://github.com/hhyo/Archery/discussions
