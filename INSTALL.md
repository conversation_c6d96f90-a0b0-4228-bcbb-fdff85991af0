# Archery 安装配置指南

## 目录
- [环境要求](#环境要求)
- [快速安装](#快速安装)
- [详细安装步骤](#详细安装步骤)
- [配置说明](#配置说明)
- [常见问题](#常见问题)
- [ODPS表级别权限功能](#odps表级别权限功能)

## 环境要求

### 系统要求
- **操作系统**: Linux, macOS, Windows
- **Python**: 3.10+
- **数据库**: MySQL 5.7+, PostgreSQL 10+, 或 SQLite (开发环境)
- **内存**: 最低 2GB，推荐 4GB+

### 必需工具
- **uv**: Python包管理工具 (推荐)
- **Git**: 版本控制工具

## 快速安装

### 一键安装脚本 (Linux/macOS)
```bash
# 下载并运行安装脚本
curl -fsSL https://raw.githubusercontent.com/hhyo/Archery/master/install.sh | bash
```

### 手动安装
```bash
# 1. 安装 uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 2. 克隆项目
git clone https://github.com/hhyo/Archery.git
cd Archery

# 3. 创建虚拟环境
uv venv --python 3.10 .venv
source .venv/bin/activate

# 4. 安装依赖
uv pip install -r requirements.txt

# 5. 初始化数据库
python manage.py migrate
python manage.py createsuperuser

# 6. 启动服务
source .venv/bin/activate && python manage.py runserver 0.0.0.0:8000
```

## 详细安装步骤

### 1. 安装 uv 包管理工具

#### macOS
```bash
# 方法1: 使用 Homebrew (推荐)
brew install uv

# 方法2: 使用官方安装脚本
curl -LsSf https://astral.sh/uv/install.sh | sh

# 方法3: 使用 pip
pip install uv
```

#### Linux (Ubuntu/Debian/CentOS)
```bash
# 官方安装脚本 (推荐)
curl -LsSf https://astral.sh/uv/install.sh | sh

# 或使用 pip
pip install uv

# 添加到 PATH (如果需要)
echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc
```

#### Windows
```powershell
# PowerShell (管理员权限)
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 或使用 pip
pip install uv

# 或使用 Chocolatey
choco install uv
```

### 2. 安装系统依赖

#### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install -y \
    python3-dev \
    python3-pip \
    default-libmysqlclient-dev \
    build-essential \
    pkg-config \
    git \
    curl
```

#### CentOS/RHEL
```bash
sudo yum update -y
sudo yum install -y \
    python3-devel \
    python3-pip \
    mysql-devel \
    gcc \
    gcc-c++ \
    pkgconfig \
    git \
    curl
```

#### macOS
```bash
# 安装 Xcode 命令行工具
xcode-select --install

# 安装 Homebrew (如果未安装)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装依赖
brew install mysql-client pkg-config git
```

### 3. 克隆项目
```bash
# 克隆最新版本
git clone https://github.com/hhyo/Archery.git
cd Archery

# 或克隆特定版本
git clone -b v1.10.0 https://github.com/hhyo/Archery.git
cd Archery
```

### 4. 创建虚拟环境
```bash
# 使用 uv 创建 Python 3.10 虚拟环境
uv venv --python 3.10 .venv

# 激活虚拟环境
# Linux/macOS:
source .venv/bin/activate

# Windows:
.venv\Scripts\activate

# 验证 Python 版本
python --version  # 应该显示 Python 3.10.x
```

### 5. 安装 Python 依赖
```bash
# 确保虚拟环境已激活
source .venv/bin/activate

# 安装所有依赖
uv pip install -r requirements.txt

# 如果某些包安装失败，可以分步安装
uv pip install Django==4.1.13
uv pip install pymongo pyecharts qrcode[pil] pyotp
uv pip install psycopg2-binary
uv pip install PyMySQL  # MySQL 连接器替代方案
```

### 6. 数据库配置

#### 使用 SQLite (开发环境，推荐)
```bash
# 无需额外配置，直接初始化
python manage.py migrate
python manage.py createsuperuser
```

#### 使用 MySQL
```bash
# 1. 创建数据库
mysql -u root -p
CREATE DATABASE archery DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'archery'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON archery.* TO 'archery'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 2. 修改配置文件
cp archery/settings.py archery/local_settings.py
# 编辑 local_settings.py 中的数据库配置

# 3. 初始化数据库
python manage.py migrate
python manage.py createsuperuser
```

#### 使用 PostgreSQL
```bash
# 1. 创建数据库
sudo -u postgres psql
CREATE DATABASE archery;
CREATE USER archery WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE archery TO archery;
\q

# 2. 修改配置文件 (同上)
# 3. 初始化数据库 (同上)
```

### 7. 启动服务
```bash
# 开发环境启动
source .venv/bin/activate && python manage.py runserver 0.0.0.0:8000

# 后台启动 (生产环境)
nohup python manage.py runserver 0.0.0.0:8000 > logs/archery.log 2>&1 &
```

### 8. 验证安装
访问以下地址验证安装：
- **主页**: http://127.0.0.1:8000/
- **管理后台**: http://127.0.0.1:8000/admin/
- **API文档**: http://127.0.0.1:8000/api/docs/

## 配置说明

### 环境变量配置
创建 `.env` 文件：
```bash
# 数据库配置
DATABASE_URL=sqlite:///db.sqlite3
# 或 MySQL: mysql://user:password@localhost:3306/archery
# 或 PostgreSQL: postgresql://user:password@localhost:5432/archery

# 安全配置
SECRET_KEY=your-secret-key-here
DEBUG=True

# 邮件配置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
```

### 实例配置
1. 登录管理后台: http://127.0.0.1:8000/admin/
2. 添加数据库实例
3. 配置资源组和权限

## 常见问题

### 依赖安装问题

#### mysqlclient 安装失败
```bash
# macOS
brew install mysql-client pkg-config
export PKG_CONFIG_PATH="/opt/homebrew/opt/mysql-client/lib/pkgconfig"
uv pip install mysqlclient

# Ubuntu/Debian
sudo apt-get install default-libmysqlclient-dev
uv pip install mysqlclient

# 使用 PyMySQL 作为替代
uv pip install PyMySQL
# 在 settings.py 中添加:
# import pymysql
# pymysql.install_as_MySQLdb()
```

#### psycopg2 安装失败
```bash
# 使用二进制版本
uv pip install psycopg2-binary

# 或安装系统依赖后重试
sudo apt-get install postgresql-dev  # Ubuntu/Debian
sudo yum install postgresql-devel     # CentOS/RHEL
```

### 启动问题

#### 端口被占用
```bash
# 查看端口占用
lsof -i :8000
netstat -tulpn | grep :8000

# 使用其他端口
python manage.py runserver 0.0.0.0:8001
```

#### 权限问题
```bash
# 确保文件权限正确
chmod +x manage.py
chown -R $USER:$USER .

# 确保日志目录存在
mkdir -p logs
chmod 755 logs
```

### 数据库问题

#### 迁移失败
```bash
# 重置迁移
python manage.py migrate --fake-initial

# 强制重新迁移
rm -rf sql/migrations/0*.py
python manage.py makemigrations
python manage.py migrate
```

## ODPS表级别权限功能

本项目已实现ODPS表级别权限管理功能：

### 功能特性
- ✅ ODPS SQL语句解析，支持表级别权限验证
- ✅ 表级别权限申请和审批流程
- ✅ 权限有效期管理
- ✅ 行数限制控制
- ✅ 完整的测试用例

### 测试环境设置
```bash
# 运行ODPS权限测试
python setup_odps_test.py
python test_odps_permissions.py
python simple_odps_test.py
```

### 使用说明
1. 配置ODPS实例连接信息
2. 设置资源组和审批流程
3. 用户申请表级别权限
4. 管理员审批权限申请
5. 用户可查询已授权的表

详细使用说明请参考项目Wiki。

## 生产环境部署

### 使用 Gunicorn
```bash
# 安装 Gunicorn
uv pip install gunicorn

# 启动服务
gunicorn --bind 0.0.0.0:8000 --workers 4 archery.wsgi:application
```

### 使用 Nginx 反向代理
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /static/ {
        alias /path/to/archery/static/;
    }
}
```

### 使用 Supervisor 进程管理
```ini
[program:archery]
command=/path/to/archery/.venv/bin/gunicorn --bind 127.0.0.1:8000 archery.wsgi:application
directory=/path/to/archery
user=archery
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/path/to/archery/logs/archery.log
```

## 支持与反馈

- **文档**: https://archerydms.com/
- **Issues**: https://github.com/hhyo/archery/issues
- **Discussions**: https://github.com/hhyo/Archery/discussions
- **Wiki**: https://github.com/hhyo/Archery/wiki
