#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试escape_string功能
"""

def manual_escape_string(value):
    """手动实现的escape_string方法"""
    if value is None:
        return "NULL"
    
    # 确保value是字符串类型
    if not isinstance(value, str):
        value = str(value)
        
    # 手动转义规则
    escape_dict = {
        '\0': '\\0',
        '\n': '\\n',
        '\r': '\\r',
        '\032': '\\Z',
        '"': '\\"',
        "'": "\\'",
        '\\': '\\\\'
    }
    return ''.join(escape_dict.get(c, c) for c in value)

def test_escape_functionality():
    """测试转义功能"""
    print("测试手动escape_string实现...")
    
    test_cases = [
        "normal_string",
        "string'with'quotes",
        'string"with"double_quotes',
        "string\\with\\backslashes",
        "string\nwith\nnewlines",
        "string\rwith\rcarriage_returns",
        "string\twith\ttabs",
        None,
        123,
        "",
    ]
    
    print("\n=== 手动转义测试 ===")
    for i, test_case in enumerate(test_cases):
        try:
            result = manual_escape_string(test_case)
            print(f"测试用例 {i+1}: {repr(test_case)} -> {repr(result)}")
        except Exception as e:
            print(f"测试用例 {i+1}: {repr(test_case)} -> 错误: {e}")

def test_pymysql_version():
    """测试PyMySQL版本和escape_string可用性"""
    try:
        import pymysql
        print(f"\nPyMySQL版本: {pymysql.__version__}")
        
        # 测试是否存在模块级别的escape_string方法
        if hasattr(pymysql, 'escape_string'):
            print("✓ pymysql.escape_string 方法存在")
            try:
                result = pymysql.escape_string("test'string")
                print(f"  测试结果: {repr(result)}")
            except Exception as e:
                print(f"  测试失败: {e}")
        else:
            print("✗ pymysql.escape_string 方法不存在（这在新版本PyMySQL中是正常的）")
            print("  需要使用我们的手动转义实现")
            
    except ImportError as e:
        print(f"无法导入PyMySQL: {e}")

if __name__ == "__main__":
    print("PyMySQL escape_string 兼容性测试")
    print("=" * 50)
    
    test_pymysql_version()
    test_escape_functionality()
    
    print("\n测试完成！")
    print("\n总结：")
    print("1. 我们的手动转义实现可以替代pymysql.escape_string")
    print("2. 代码修改后可以兼容新版本的PyMySQL")
    print("3. 建议在生产环境中使用参数化查询而不是字符串拼接")
