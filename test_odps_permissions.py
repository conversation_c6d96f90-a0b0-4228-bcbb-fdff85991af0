#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
ODPS表级别权限功能测试脚本
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archery.settings')
django.setup()

from sql.models import Instance, QueryPrivileges, Users
from sql.query_privileges import query_priv_check


def test_odps_table_privilege():
    """测试ODPS表级别权限检查"""
    print("🧪 开始测试ODPS表级别权限功能...")
    print()
    
    try:
        # 获取ODPS实例
        instance = Instance.objects.filter(db_type='odps').first()
        if not instance:
            print("❌ 未找到ODPS实例，请先运行 setup_odps_test.py")
            return False
        
        print(f"✅ 找到ODPS实例: {instance.instance_name}")
        
        # 获取测试用户
        user = Users.objects.filter(username='test_user').first()
        if not user:
            print("❌ 未找到测试用户，请先运行 setup_odps_test.py")
            return False
        
        print(f"✅ 找到测试用户: {user.username}")
        
        # 测试SQL语句
        test_sqls = [
            "SELECT * FROM dev_dlf.test_table LIMIT 10",
            "SELECT count(*) FROM dev_dlf.test_table",
            "SELECT * FROM dev_dlf.another_table LIMIT 10",  # 无权限的表
        ]
        
        print()
        print("📝 测试SQL权限检查:")
        print("-" * 50)
        
        for i, sql in enumerate(test_sqls, 1):
            print(f"{i}. 测试SQL: {sql}")
            
            try:
                # 调用权限检查函数，添加limit_num参数
                result = query_priv_check(user, instance, instance.db_name, sql, 1000)

                if result.get('status') == 0:
                    print(f"   ✅ 权限检查通过")
                else:
                    print(f"   ❌ 权限检查失败: {result.get('msg', '未知错误')}")

            except Exception as e:
                print(f"   ⚠️  权限检查异常: {str(e)}")
            
            print()
        
        # 检查权限记录
        print("📊 当前权限记录:")
        print("-" * 50)
        
        privileges = QueryPrivileges.objects.filter(
            user_name=user.username,
            instance=instance
        )
        
        if privileges.exists():
            for priv in privileges:
                priv_type_name = "数据库级别" if priv.priv_type == 1 else "表级别"
                table_info = f".{priv.table_name}" if priv.table_name else ""
                print(f"   - {priv_type_name}: {priv.db_name}{table_info}")
        else:
            print("   无权限记录")
        
        print()
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        return False


def test_odps_sql_parsing():
    """测试ODPS SQL解析功能"""
    print("🔍 测试ODPS SQL解析功能...")
    print()
    
    from sql.query_privileges import _odps_table_ref
    
    # 获取ODPS实例
    instance = Instance.objects.filter(db_type='odps').first()
    if not instance:
        print("❌ 未找到ODPS实例")
        return False
    
    test_sqls = [
        "SELECT * FROM dev_dlf.test_table",
        "SELECT a.*, b.name FROM dev_dlf.table1 a JOIN dev_dlf.table2 b ON a.id = b.id",
        "INSERT INTO dev_dlf.target_table SELECT * FROM dev_dlf.source_table",
        "SELECT * FROM another_project.table1",
    ]
    
    print("📝 测试SQL解析:")
    print("-" * 50)
    
    for i, sql in enumerate(test_sqls, 1):
        print(f"{i}. SQL: {sql}")
        
        try:
            tables = _odps_table_ref(sql, instance, instance.db_name)
            if tables:
                # 格式化表名显示
                table_names = [f"{table['schema']}.{table['name']}" for table in tables]
                print(f"   解析到的表: {', '.join(table_names)}")
            else:
                print(f"   未解析到表")
        except Exception as e:
            print(f"   解析异常: {str(e)}")
        
        print()
    
    return True


def main():
    """主函数"""
    print("🚀 ODPS表级别权限功能测试")
    print("=" * 60)
    print()
    
    # 测试SQL解析
    if not test_odps_sql_parsing():
        sys.exit(1)
    
    print()
    
    # 测试权限检查
    if not test_odps_table_privilege():
        sys.exit(1)
    
    print("🎉 所有测试完成！")


if __name__ == '__main__':
    main()
