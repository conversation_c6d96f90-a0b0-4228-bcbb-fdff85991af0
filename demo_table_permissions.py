#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
演示表权限功能的脚本
这个脚本展示了修改后的功能如何根据用户权限过滤表列表
"""

import os
import sys
import django
import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archery.settings')
django.setup()

from sql.models import Instance, QueryPrivileges, Users
from sql.instance import get_user_authorized_tables


def create_demo_data():
    """创建演示数据"""
    print("创建演示数据...")
    
    # 创建测试实例
    instance, created = Instance.objects.get_or_create(
        instance_name='demo_instance',
        defaults={
            'type': 'slave',
            'db_type': 'mysql',
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'demo',
            'password': 'demo'
        }
    )
    
    # 创建普通用户
    user, created = Users.objects.get_or_create(
        username='demo_user',
        defaults={
            'email': '<EMAIL>',
            'is_active': True
        }
    )
    if created:
        user.set_password('demo123')
        user.save()
    
    # 创建管理员用户
    admin_user, created = Users.objects.get_or_create(
        username='demo_admin',
        defaults={
            'email': '<EMAIL>',
            'is_superuser': True,
            'is_staff': True,
            'is_active': True
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
    
    # 清理旧的权限记录
    QueryPrivileges.objects.filter(
        user_name__in=['demo_user'],
        instance=instance
    ).delete()
    
    # 为普通用户创建表权限
    tables_with_permission = ['users', 'orders', 'products']
    for table_name in tables_with_permission:
        QueryPrivileges.objects.create(
            user_name=user.username,
            user_display=user.get_full_name() or user.username,
            instance=instance,
            db_name='demo_db',
            table_name=table_name,
            valid_date=datetime.date.today() + datetime.timedelta(days=30),
            limit_num=1000,
            priv_type=2  # 表权限
        )
    
    print(f"✓ 创建实例: {instance.instance_name}")
    print(f"✓ 创建普通用户: {user.username}")
    print(f"✓ 创建管理员用户: {admin_user.username}")
    print(f"✓ 为用户 {user.username} 创建表权限: {', '.join(tables_with_permission)}")
    
    return instance, user, admin_user


def demo_table_permissions():
    """演示表权限功能"""
    print("\n" + "="*60)
    print("演示：根据用户权限过滤表列表")
    print("="*60)
    
    instance, user, admin_user = create_demo_data()
    
    # 模拟数据库中的所有表
    all_tables = ['users', 'orders', 'products', 'logs', 'configs', 'reports']
    
    print(f"\n假设数据库 'demo_db' 中有以下所有表:")
    print(f"  {', '.join(all_tables)}")
    
    # 测试普通用户
    print(f"\n1. 普通用户 '{user.username}' 的权限:")
    user_tables = get_user_authorized_tables(user, instance, 'demo_db')
    print(f"   有权限的表: {user_tables}")
    print(f"   说明: 用户只能看到已授权的表，而不是所有表")
    
    # 测试管理员用户
    print(f"\n2. 管理员用户 '{admin_user.username}' 的权限:")
    print(f"   有权限的表: 所有表 (管理员拥有全部权限)")
    print(f"   说明: 管理员可以看到所有表")
    
    # 测试没有权限的用户
    no_perm_user, created = Users.objects.get_or_create(
        username='no_perm_user',
        defaults={
            'email': '<EMAIL>',
            'is_active': True
        }
    )
    
    print(f"\n3. 无权限用户 '{no_perm_user.username}' 的权限:")
    no_perm_tables = get_user_authorized_tables(no_perm_user, instance, 'demo_db')
    print(f"   有权限的表: {no_perm_tables}")
    print(f"   说明: 没有任何权限的用户看不到任何表")
    
    print(f"\n" + "="*60)
    print("功能说明:")
    print("- 修改前: 在线查询页面显示所有表，不管用户是否有权限")
    print("- 修改后: 在线查询页面只显示用户有权限的表")
    print("- 这样可以提高用户体验，避免用户看到无法查询的表")
    print("="*60)


if __name__ == '__main__':
    try:
        demo_table_permissions()
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
