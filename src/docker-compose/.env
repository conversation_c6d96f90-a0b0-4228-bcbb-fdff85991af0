NGINX_PORT=9123

# https://django-environ.readthedocs.io/en/latest/quickstart.html#usage
# https://docs.djangoproject.com/zh-hans/4.1/ref/settings/
DEBUG=false
DATABASE_URL=mysql://root:123456@mysql:3306/archery
CACHE_URL=redis://redis:6379/0?PASSWORD=123456

# https://docs.djangoproject.com/en/4.0/ref/settings/#csrf-trusted-origins
CSRF_TRUSTED_ORIGINS=http://127.0.0.1:9123

# https://django-auth-ldap.readthedocs.io/en/latest/
ENABLE_LDAP=false
AUTH_LDAP_ALWAYS_UPDATE_USER=true
AUTH_LDAP_USER_ATTR_MAP=username=cn,display=displayname,email=email

# https://django-q.readthedocs.io/en/latest/configure.html#
Q_CLUISTER_WORKERS=4
Q_CLUISTER_TIMEOUT=60
Q_CLUISTER_SYNC=false

# 在网站标题及登录页面追加此内容, 可用于多archery实例的区分。Archery后台也有相同配置，如都做了配置，以后台配置为准
CUSTOM_TITLE_SUFFIX = ""

