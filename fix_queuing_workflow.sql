-- =====================================================
-- 工单执行排队中问题修复SQL脚本
-- =====================================================

-- 1. 查看当前排队中的工单
SELECT 
    id,
    workflow_name,
    engineer_display,
    status,
    create_time,
    instance_id,
    db_name,
    TIMESTAMPDIFF(MINUTE, create_time, NOW()) as queue_minutes
FROM sql_workflow 
WHERE status = 'workflow_queuing'
ORDER BY create_time DESC;

-- 2. 查看执行中的工单（可能卡住）
SELECT 
    id,
    workflow_name,
    engineer_display,
    status,
    create_time,
    instance_id,
    db_name,
    TIMESTAMPDIFF(MINUTE, create_time, NOW()) as executing_minutes
FROM sql_workflow 
WHERE status = 'workflow_executing'
ORDER BY create_time DESC;

-- 3. 查看Django-Q任务表（如果存在）
-- 注意：这个表可能不存在，取决于Django-Q配置
SELECT 
    id,
    name,
    func,
    started,
    stopped,
    success
FROM django_q_task 
WHERE name LIKE '%sqlreview-execute%'
ORDER BY started DESC
LIMIT 20;

-- =====================================================
-- 修复方案1：将长时间排队的工单重置为审核通过状态
-- =====================================================

-- 查看超过30分钟还在排队的工单
SELECT 
    id,
    workflow_name,
    engineer_display,
    status,
    create_time,
    TIMESTAMPDIFF(MINUTE, create_time, NOW()) as queue_minutes
FROM sql_workflow 
WHERE status = 'workflow_queuing'
AND TIMESTAMPDIFF(MINUTE, create_time, NOW()) > 30;

-- 将超过30分钟的排队工单重置为审核通过状态（需要手动确认）
-- 注意：执行前请确认这些工单确实卡住了
/*
UPDATE sql_workflow 
SET status = 'workflow_review_pass'
WHERE status = 'workflow_queuing'
AND TIMESTAMPDIFF(MINUTE, create_time, NOW()) > 30;
*/

-- =====================================================
-- 修复方案2：将长时间执行中的工单标记为异常
-- =====================================================

-- 查看超过60分钟还在执行中的工单
SELECT 
    id,
    workflow_name,
    engineer_display,
    status,
    create_time,
    TIMESTAMPDIFF(MINUTE, create_time, NOW()) as executing_minutes
FROM sql_workflow 
WHERE status = 'workflow_executing'
AND TIMESTAMPDIFF(MINUTE, create_time, NOW()) > 60;

-- 将超过60分钟的执行中工单标记为异常（需要手动确认）
-- 注意：执行前请确认这些工单确实卡住了
/*
UPDATE sql_workflow 
SET status = 'workflow_exception',
    finish_time = NOW()
WHERE status = 'workflow_executing'
AND TIMESTAMPDIFF(MINUTE, create_time, NOW()) > 60;
*/

-- =====================================================
-- 修复方案3：针对特定工单ID的修复
-- =====================================================

-- 将特定工单ID重置为审核通过状态
-- 替换 YOUR_WORKFLOW_ID 为实际的工单ID
/*
UPDATE sql_workflow 
SET status = 'workflow_review_pass'
WHERE id = YOUR_WORKFLOW_ID
AND status IN ('workflow_queuing', 'workflow_executing');
*/

-- 将特定工单ID标记为异常
-- 替换 YOUR_WORKFLOW_ID 为实际的工单ID
/*
UPDATE sql_workflow 
SET status = 'workflow_exception',
    finish_time = NOW()
WHERE id = YOUR_WORKFLOW_ID
AND status IN ('workflow_queuing', 'workflow_executing');
*/

-- =====================================================
-- 修复方案4：批量处理今天的卡住工单
-- =====================================================

-- 查看今天创建的排队中工单
SELECT 
    id,
    workflow_name,
    engineer_display,
    status,
    create_time,
    TIMESTAMPDIFF(MINUTE, create_time, NOW()) as queue_minutes
FROM sql_workflow 
WHERE status = 'workflow_queuing'
AND DATE(create_time) = CURDATE()
ORDER BY create_time DESC;

-- 将今天超过15分钟的排队工单重置（谨慎使用）
/*
UPDATE sql_workflow 
SET status = 'workflow_review_pass'
WHERE status = 'workflow_queuing'
AND DATE(create_time) = CURDATE()
AND TIMESTAMPDIFF(MINUTE, create_time, NOW()) > 15;
*/

-- =====================================================
-- 查询相关审核日志
-- =====================================================

-- 查看工单的审核日志（替换 YOUR_WORKFLOW_ID）
/*
SELECT 
    wl.operation_type_desc,
    wl.operation_info,
    wl.operator_display,
    wl.operation_time
FROM workflow_log wl
JOIN workflow_audit wa ON wl.audit_id = wa.audit_id
WHERE wa.workflow_id = YOUR_WORKFLOW_ID
AND wa.workflow_type = 2  -- SQL上线申请
ORDER BY wl.operation_time DESC;
*/

-- =====================================================
-- 预防措施：清理Django-Q任务表
-- =====================================================

-- 清理失败的Django-Q任务（如果表存在）
/*
DELETE FROM django_q_task 
WHERE success = 0 
AND stopped < DATE_SUB(NOW(), INTERVAL 1 DAY);
*/

-- 清理成功的旧Django-Q任务（如果表存在）
/*
DELETE FROM django_q_task 
WHERE success = 1 
AND stopped < DATE_SUB(NOW(), INTERVAL 7 DAY);
*/
