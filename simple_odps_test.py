#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
简化的ODPS表级别权限测试脚本
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archery.settings')
django.setup()

from sql.models import Instance, QueryPrivileges, Users


def test_odps_setup():
    """测试ODPS设置是否正确"""
    print("🧪 测试ODPS设置...")
    print()
    
    try:
        # 检查ODPS实例
        instance = Instance.objects.filter(db_type='odps').first()
        if not instance:
            print("❌ 未找到ODPS实例")
            return False
        
        print(f"✅ 找到ODPS实例: {instance.instance_name}")
        print(f"   - 数据库类型: {instance.db_type}")
        print(f"   - 连接地址: {instance.host}")
        print(f"   - 项目名: {instance.db_name}")
        print()
        
        # 检查测试用户
        user = Users.objects.filter(username='test_user').first()
        if not user:
            print("❌ 未找到测试用户")
            return False
        
        print(f"✅ 找到测试用户: {user.username}")
        print()
        
        # 检查权限记录
        privileges = QueryPrivileges.objects.filter(
            user_name=user.username,
            instance=instance
        )
        
        print("📊 权限记录:")
        print("-" * 50)
        
        if privileges.exists():
            for priv in privileges:
                priv_type_name = "数据库级别" if priv.priv_type == 1 else "表级别"
                table_info = f".{priv.table_name}" if priv.table_name else ""
                print(f"   - {priv_type_name}: {priv.db_name}{table_info}")
                print(f"     有效期: {priv.valid_date}")
                print(f"     行数限制: {priv.limit_num}")
        else:
            print("   无权限记录")
        
        print()
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        return False


def test_odps_db_choices():
    """测试ODPS数据库类型选择"""
    print("🔍 测试数据库类型选择...")
    print()
    
    from sql.models import DB_TYPE_CHOICES
    
    # 查找ODPS选项
    odps_found = False
    for choice in DB_TYPE_CHOICES:
        if choice[0] == 'odps':
            odps_found = True
            print(f"✅ 找到ODPS数据库类型: {choice}")
            break
    
    if not odps_found:
        print("❌ 未找到ODPS数据库类型选项")
        return False
    
    print()
    return True


def test_privilege_types():
    """测试权限类型"""
    print("📝 测试权限类型...")
    print()
    
    # 检查QueryPrivileges模型的权限类型选择
    from sql.models import QueryPrivileges
    
    # 获取权限类型选择
    priv_type_choices = QueryPrivileges._meta.get_field('priv_type').choices
    
    print("权限类型选择:")
    for choice in priv_type_choices:
        print(f"   - {choice[0]}: {choice[1]}")
    
    # 检查是否包含表级别权限
    table_priv_found = any(choice[0] == 2 and choice[1] == 'TABLE' for choice in priv_type_choices)
    
    if table_priv_found:
        print("✅ 找到表级别权限类型")
    else:
        print("❌ 未找到表级别权限类型")
        return False
    
    print()
    return True


def main():
    """主函数"""
    print("🚀 ODPS表级别权限功能简化测试")
    print("=" * 60)
    print()
    
    # 测试数据库类型选择
    if not test_odps_db_choices():
        sys.exit(1)
    
    # 测试权限类型
    if not test_privilege_types():
        sys.exit(1)
    
    # 测试ODPS设置
    if not test_odps_setup():
        sys.exit(1)
    
    print("🎉 所有基础测试通过！")
    print()
    print("📋 下一步操作:")
    print("1. 访问实例管理页面配置真实的ODPS连接信息")
    print("2. 访问权限申请页面测试表级别权限申请")
    print("3. 访问SQL查询页面测试权限验证")
    print()
    print("🔗 相关链接:")
    print("   - 实例管理: http://127.0.0.1:8000/instance/")
    print("   - 权限申请: http://127.0.0.1:8000/queryapplylist/")
    print("   - SQL查询: http://127.0.0.1:8000/sqlquery/")


if __name__ == '__main__':
    main()
