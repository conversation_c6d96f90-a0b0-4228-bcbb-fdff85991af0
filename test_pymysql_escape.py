#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PyMySQL escape_string功能的脚本
用于验证升级到PyMySQL 1.4.0后escape_string方法是否正常工作
"""

import sys
import os

# 添加项目路径到sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_escape_string():
    """测试escape_string功能"""
    print("测试PyMySQL escape_string功能...")
    
    # 测试用例
    test_cases = [
        "normal_string",
        "string'with'quotes",
        'string"with"double_quotes',
        "string\\with\\backslashes",
        "string\nwith\nnewlines",
        "string\rwith\rcarriage_returns",
        "string\twith\ttabs",
        "string\0with\0null_chars",
        "string\032with\032substitute_chars",
        None,
        123,
        "",
    ]
    
    try:
        # 测试MySQL引擎的escape_string方法
        from sql.engines.mysql import MysqlEngine
        from sql.models import Instance
        
        # 创建一个模拟的实例对象
        class MockInstance:
            def __init__(self):
                self.host = "localhost"
                self.port = 3306
                self.user = "test"
                self.password = "test"
                self.db_name = "test"
                self.charset = "utf8mb4"
                
        mock_instance = MockInstance()
        mysql_engine = MysqlEngine(instance=mock_instance)
        
        print("\n=== MySQL引擎escape_string测试 ===")
        for i, test_case in enumerate(test_cases):
            try:
                result = mysql_engine.escape_string(test_case)
                print(f"测试用例 {i+1}: {repr(test_case)} -> {repr(result)}")
            except Exception as e:
                print(f"测试用例 {i+1}: {repr(test_case)} -> 错误: {e}")
                
    except Exception as e:
        print(f"MySQL引擎测试失败: {e}")
    
    try:
        # 测试GoInception引擎的escape_string方法
        from sql.engines.goinception import GoInceptionEngine
        
        goinception_engine = GoInceptionEngine()
        
        print("\n=== GoInception引擎escape_string测试 ===")
        for i, test_case in enumerate(test_cases):
            try:
                result = goinception_engine.escape_string(test_case)
                print(f"测试用例 {i+1}: {repr(test_case)} -> {repr(result)}")
            except Exception as e:
                print(f"测试用例 {i+1}: {repr(test_case)} -> 错误: {e}")
                
    except Exception as e:
        print(f"GoInception引擎测试失败: {e}")

def test_pymysql_version():
    """测试PyMySQL版本"""
    try:
        import pymysql
        print(f"\nPyMySQL版本: {pymysql.__version__}")
        
        # 测试是否存在模块级别的escape_string方法
        if hasattr(pymysql, 'escape_string'):
            print("✓ pymysql.escape_string 方法存在")
            try:
                result = pymysql.escape_string("test'string")
                print(f"  测试结果: {repr(result)}")
            except Exception as e:
                print(f"  测试失败: {e}")
        else:
            print("✗ pymysql.escape_string 方法不存在（这在PyMySQL 1.4.0+中是正常的）")
            
    except ImportError as e:
        print(f"无法导入PyMySQL: {e}")

if __name__ == "__main__":
    print("PyMySQL升级兼容性测试")
    print("=" * 50)
    
    test_pymysql_version()
    test_escape_string()
    
    print("\n测试完成！")
