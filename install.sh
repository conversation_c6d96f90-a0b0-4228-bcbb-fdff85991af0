#!/bin/bash

# Archery 一键安装脚本
# 支持 Linux 和 macOS

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查操作系统
check_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        if command -v apt-get >/dev/null 2>&1; then
            DISTRO="debian"
        elif command -v yum >/dev/null 2>&1; then
            DISTRO="rhel"
        else
            log_error "不支持的 Linux 发行版"
            exit 1
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    log_info "检测到操作系统: $OS"
}

# 检查 Python 版本
check_python() {
    if command -v python3 >/dev/null 2>&1; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        log_info "检测到 Python 版本: $PYTHON_VERSION"
        
        # 检查是否为 3.10+
        if python3 -c 'import sys; exit(0 if sys.version_info >= (3, 10) else 1)' 2>/dev/null; then
            log_success "Python 版本满足要求 (3.10+)"
        else
            log_warning "Python 版本过低，建议升级到 3.10+"
        fi
    else
        log_error "未找到 Python3，请先安装 Python 3.10+"
        exit 1
    fi
}

# 安装系统依赖
install_system_deps() {
    log_info "安装系统依赖..."
    
    if [[ "$OS" == "linux" ]]; then
        if [[ "$DISTRO" == "debian" ]]; then
            sudo apt-get update
            sudo apt-get install -y \
                python3-dev \
                python3-pip \
                python3-venv \
                default-libmysqlclient-dev \
                build-essential \
                pkg-config \
                git \
                curl
        elif [[ "$DISTRO" == "rhel" ]]; then
            sudo yum update -y
            sudo yum install -y \
                python3-devel \
                python3-pip \
                mysql-devel \
                gcc \
                gcc-c++ \
                pkgconfig \
                git \
                curl
        fi
    elif [[ "$OS" == "macos" ]]; then
        # 检查是否安装了 Homebrew
        if ! command -v brew >/dev/null 2>&1; then
            log_info "安装 Homebrew..."
            /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        fi
        
        # 安装依赖
        brew install mysql-client pkg-config git
    fi
    
    log_success "系统依赖安装完成"
}

# 安装 uv
install_uv() {
    if command -v uv >/dev/null 2>&1; then
        log_success "uv 已安装"
        return
    fi
    
    log_info "安装 uv 包管理工具..."
    
    if [[ "$OS" == "macos" ]] && command -v brew >/dev/null 2>&1; then
        brew install uv
    else
        curl -LsSf https://astral.sh/uv/install.sh | sh
        
        # 添加到 PATH
        if [[ -f "$HOME/.local/bin/uv" ]]; then
            export PATH="$HOME/.local/bin:$PATH"
            echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
        fi
    fi
    
    # 验证安装
    if command -v uv >/dev/null 2>&1; then
        log_success "uv 安装成功: $(uv --version)"
    else
        log_error "uv 安装失败"
        exit 1
    fi
}

# 克隆项目
clone_project() {
    PROJECT_DIR="Archery"
    
    if [[ -d "$PROJECT_DIR" ]]; then
        log_warning "目录 $PROJECT_DIR 已存在，是否删除并重新克隆? (y/N)"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            rm -rf "$PROJECT_DIR"
        else
            log_info "使用现有目录"
            cd "$PROJECT_DIR"
            return
        fi
    fi
    
    log_info "克隆 Archery 项目..."
    git clone https://github.com/hhyo/Archery.git
    cd "$PROJECT_DIR"
    log_success "项目克隆完成"
}

# 创建虚拟环境
create_venv() {
    log_info "创建 Python 3.10 虚拟环境..."
    
    # 检查是否已存在虚拟环境
    if [[ -d ".venv" ]]; then
        log_warning "虚拟环境已存在，是否重新创建? (y/N)"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            rm -rf .venv
        else
            log_info "使用现有虚拟环境"
            return
        fi
    fi
    
    uv venv --python 3.10 .venv
    log_success "虚拟环境创建完成"
}

# 安装 Python 依赖
install_python_deps() {
    log_info "安装 Python 依赖包..."
    
    # 激活虚拟环境
    source .venv/bin/activate
    
    # 升级 pip
    python -m pip install --upgrade pip
    
    # 安装依赖
    if [[ -f "requirements.txt" ]]; then
        uv pip install -r requirements.txt
    else
        log_error "未找到 requirements.txt 文件"
        exit 1
    fi
    
    # 安装可能缺失的依赖
    log_info "安装额外依赖..."
    uv pip install pymongo pyecharts qrcode[pil] pyotp psycopg2-binary PyMySQL
    
    log_success "Python 依赖安装完成"
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 激活虚拟环境
    source .venv/bin/activate
    
    # 运行迁移
    python manage.py migrate
    
    # 创建超级用户
    log_info "创建管理员用户..."
    echo "请输入管理员用户信息:"
    python manage.py createsuperuser
    
    log_success "数据库初始化完成"
}

# 创建启动脚本
create_start_script() {
    log_info "创建启动脚本..."
    
    cat > start.sh << 'EOF'
#!/bin/bash

# Archery 启动脚本

# 激活虚拟环境并启动服务
source .venv/bin/activate && python manage.py runserver 0.0.0.0:8000
EOF

    chmod +x start.sh
    log_success "启动脚本创建完成: ./start.sh"
}

# 显示完成信息
show_completion_info() {
    log_success "🎉 Archery 安装完成!"
    echo
    echo "📋 安装信息:"
    echo "  - 项目目录: $(pwd)"
    echo "  - Python 版本: $(python3 --version)"
    echo "  - 虚拟环境: .venv"
    echo
    echo "🚀 启动命令:"
    echo "  cd $(pwd)"
    echo "  source .venv/bin/activate && python manage.py runserver 0.0.0.0:8000"
    echo "  # 或直接运行: ./start.sh"
    echo
    echo "🔗 访问地址:"
    echo "  - 主页: http://127.0.0.1:8000/"
    echo "  - 管理后台: http://127.0.0.1:8000/admin/"
    echo "  - API文档: http://127.0.0.1:8000/api/docs/"
    echo
    echo "📚 更多信息:"
    echo "  - 安装文档: INSTALL.md"
    echo "  - 项目文档: https://archerydms.com/"
    echo "  - GitHub: https://github.com/hhyo/Archery"
}

# 主函数
main() {
    echo "🚀 Archery 一键安装脚本"
    echo "================================"
    
    # 检查环境
    check_os
    check_python
    
    # 安装依赖
    install_system_deps
    install_uv
    
    # 安装项目
    clone_project
    create_venv
    install_python_deps
    init_database
    create_start_script
    
    # 显示完成信息
    show_completion_info
}

# 错误处理
trap 'log_error "安装过程中发生错误，请检查日志"; exit 1' ERR

# 运行主函数
main "$@"
