#!/bin/bash

# Archery MySQL 数据库备份脚本
# 使用方法: ./mysql_backup.sh

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 配置变量
DB_HOST="127.0.0.1"
DB_PORT="3306"
DB_NAME="archery"
DB_USER="root"
DB_PASS="123456"

# 备份目录
BACKUP_DIR="backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="${BACKUP_DIR}/archery_backup_${DATE}.sql"
BACKUP_COMPRESSED="${BACKUP_FILE}.gz"

echo -e "${BLUE}🗃️  MySQL 数据库备份脚本${NC}"
echo -e "数据库: ${DB_NAME}@${DB_HOST}:${DB_PORT}"
echo -e "备份时间: $(date)"
echo ""

# 创建备份目录
if [[ ! -d "$BACKUP_DIR" ]]; then
    echo -e "${YELLOW}📁 创建备份目录: $BACKUP_DIR${NC}"
    mkdir -p "$BACKUP_DIR"
fi

# 检查 MySQL 连接
echo -e "${BLUE}🔍 检查 MySQL 连接...${NC}"
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "SELECT 1;" "$DB_NAME" > /dev/null 2>&1; then
    echo -e "${RED}❌ 无法连接到 MySQL 数据库${NC}"
    echo "请检查数据库连接参数和服务状态"
    exit 1
fi

echo -e "${GREEN}✅ MySQL 连接正常${NC}"

# 执行备份
echo -e "${BLUE}📦 开始备份数据库...${NC}"
if mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" \
    --single-transaction \
    "$DB_NAME" > "$BACKUP_FILE" 2>&1; then
    
    echo -e "${GREEN}✅ 数据库备份完成: $BACKUP_FILE${NC}"
    
    # 压缩备份文件
    echo -e "${BLUE}🔄 压缩备份文件...${NC}"
    if gzip "$BACKUP_FILE"; then
        echo -e "${GREEN}✅ 备份文件已压缩: $BACKUP_COMPRESSED${NC}"
        FINAL_BACKUP="$BACKUP_COMPRESSED"
    else
        echo -e "${YELLOW}⚠️  压缩失败，保留原始备份文件${NC}"
        FINAL_BACKUP="$BACKUP_FILE"
    fi
    
    # 显示备份文件信息
    BACKUP_SIZE=$(du -h "$FINAL_BACKUP" | cut -f1)
    echo ""
    echo -e "${GREEN}🎉 备份成功完成！${NC}"
    echo -e "备份文件: $FINAL_BACKUP"
    echo -e "文件大小: $BACKUP_SIZE"
    echo ""
    
    # 清理旧备份 (保留最近7天)
    echo -e "${BLUE}🧹 清理旧备份文件...${NC}"
    find "$BACKUP_DIR" -name "archery_backup_*.sql*" -type f -mtime +7 -delete 2>/dev/null || true
    
    REMAINING_BACKUPS=$(find "$BACKUP_DIR" -name "archery_backup_*.sql*" -type f | wc -l)
    echo -e "${GREEN}✅ 备份清理完成，当前保留 $REMAINING_BACKUPS 个备份文件${NC}"
    
else
    echo -e "${RED}❌ 数据库备份失败${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}💡 恢复数据库命令:${NC}"
if [[ "$FINAL_BACKUP" == *.gz ]]; then
    echo "gunzip -c \"$FINAL_BACKUP\" | mysql -h\"$DB_HOST\" -P\"$DB_PORT\" -u\"$DB_USER\" -p\"$DB_PASS\""
else
    echo "mysql -h\"$DB_HOST\" -P\"$DB_PORT\" -u\"$DB_USER\" -p\"$DB_PASS\" < \"$FINAL_BACKUP\""
fi
echo ""