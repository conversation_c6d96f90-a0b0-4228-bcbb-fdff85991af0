#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工单执行排队问题诊断和修复脚本
"""

import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'archery.settings')
django.setup()

from django.db import transaction
from sql.models import SqlWorkflow
from django_q.models import Task
from django_q.tasks import async_task
from sql.utils.execute_sql import execute
from common.utils.const import WorkflowType
from sql.utils.workflow_audit import Audit

def check_queuing_workflows():
    """检查排队中的工单"""
    print("=== 检查排队中的工单 ===")
    
    queuing_workflows = SqlWorkflow.objects.filter(
        status='workflow_queuing'
    ).order_by('-create_time')
    
    if not queuing_workflows:
        print("✓ 没有发现排队中的工单")
        return []
    
    print(f"发现 {queuing_workflows.count()} 个排队中的工单：")
    
    stuck_workflows = []
    for workflow in queuing_workflows:
        queue_time = datetime.now() - workflow.create_time.replace(tzinfo=None)
        queue_minutes = queue_time.total_seconds() / 60
        
        print(f"工单ID: {workflow.id}")
        print(f"  工单名称: {workflow.workflow_name}")
        print(f"  提交人: {workflow.engineer_display}")
        print(f"  创建时间: {workflow.create_time}")
        print(f"  排队时长: {queue_minutes:.1f} 分钟")
        print(f"  实例: {workflow.instance.instance_name}")
        print(f"  数据库: {workflow.db_name}")
        
        # 超过15分钟认为可能卡住
        if queue_minutes > 15:
            stuck_workflows.append(workflow)
            print("  ⚠️  可能卡住了")
        print()
    
    return stuck_workflows

def check_executing_workflows():
    """检查执行中的工单"""
    print("=== 检查执行中的工单 ===")
    
    executing_workflows = SqlWorkflow.objects.filter(
        status='workflow_executing'
    ).order_by('-create_time')
    
    if not executing_workflows:
        print("✓ 没有发现执行中的工单")
        return []
    
    print(f"发现 {executing_workflows.count()} 个执行中的工单：")
    
    stuck_workflows = []
    for workflow in executing_workflows:
        # 使用create_time作为执行开始时间的近似值
        execute_time = datetime.now() - workflow.create_time.replace(tzinfo=None)
        execute_minutes = execute_time.total_seconds() / 60
        
        print(f"工单ID: {workflow.id}")
        print(f"  工单名称: {workflow.workflow_name}")
        print(f"  提交人: {workflow.engineer_display}")
        print(f"  创建时间: {workflow.create_time}")
        print(f"  执行时长: {execute_minutes:.1f} 分钟")
        print(f"  实例: {workflow.instance.instance_name}")
        print(f"  数据库: {workflow.db_name}")
        
        # 超过60分钟认为可能卡住
        if execute_minutes > 60:
            stuck_workflows.append(workflow)
            print("  ⚠️  可能卡住了")
        print()
    
    return stuck_workflows

def check_django_q_tasks():
    """检查Django-Q任务状态"""
    print("=== 检查Django-Q任务状态 ===")
    
    try:
        # 查看最近的SQL执行任务
        recent_tasks = Task.objects.filter(
            name__contains='sqlreview-execute'
        ).order_by('-started')[:10]
        
        if not recent_tasks:
            print("没有发现相关的Django-Q任务")
            return
        
        print(f"最近 {recent_tasks.count()} 个SQL执行任务：")
        for task in recent_tasks:
            print(f"任务ID: {task.id}")
            print(f"  任务名称: {task.name}")
            print(f"  开始时间: {task.started}")
            print(f"  结束时间: {task.stopped}")
            print(f"  执行成功: {task.success}")
            if not task.success and task.result:
                print(f"  错误信息: {task.result}")
            print()
            
    except Exception as e:
        print(f"检查Django-Q任务失败: {e}")

def fix_stuck_queuing_workflows(workflow_ids=None, auto_confirm=False):
    """修复卡住的排队工单"""
    print("=== 修复排队中的工单 ===")
    
    if workflow_ids:
        workflows = SqlWorkflow.objects.filter(
            id__in=workflow_ids,
            status='workflow_queuing'
        )
    else:
        # 修复超过30分钟的排队工单
        cutoff_time = datetime.now() - timedelta(minutes=30)
        workflows = SqlWorkflow.objects.filter(
            status='workflow_queuing',
            create_time__lt=cutoff_time
        )
    
    if not workflows:
        print("没有需要修复的排队工单")
        return
    
    print(f"发现 {workflows.count()} 个需要修复的工单：")
    for workflow in workflows:
        print(f"  工单ID: {workflow.id} - {workflow.workflow_name}")
    
    if not auto_confirm:
        confirm = input("\n是否将这些工单重置为审核通过状态？(y/N): ")
        if confirm.lower() != 'y':
            print("操作已取消")
            return
    
    # 批量更新状态
    with transaction.atomic():
        updated_count = workflows.update(status='workflow_review_pass')
        print(f"✓ 已修复 {updated_count} 个工单")
        
        # 添加日志
        for workflow in workflows:
            try:
                audit_id = Audit.detail_by_workflow_id(
                    workflow_id=workflow.id, 
                    workflow_type=WorkflowType.SQL_REVIEW
                ).audit_id
                Audit.add_log(
                    audit_id=audit_id,
                    operation_type=6,
                    operation_type_desc="系统修复",
                    operation_info="工单排队超时，系统自动重置为审核通过状态",
                    operator="system",
                    operator_display="系统",
                )
            except Exception as e:
                print(f"添加日志失败 (工单ID: {workflow.id}): {e}")

def fix_stuck_executing_workflows(workflow_ids=None, auto_confirm=False):
    """修复卡住的执行中工单"""
    print("=== 修复执行中的工单 ===")
    
    if workflow_ids:
        workflows = SqlWorkflow.objects.filter(
            id__in=workflow_ids,
            status='workflow_executing'
        )
    else:
        # 修复超过60分钟的执行工单
        cutoff_time = datetime.now() - timedelta(minutes=60)
        workflows = SqlWorkflow.objects.filter(
            status='workflow_executing',
            create_time__lt=cutoff_time
        )
    
    if not workflows:
        print("没有需要修复的执行中工单")
        return
    
    print(f"发现 {workflows.count()} 个需要修复的工单：")
    for workflow in workflows:
        print(f"  工单ID: {workflow.id} - {workflow.workflow_name}")
    
    if not auto_confirm:
        confirm = input("\n是否将这些工单标记为异常状态？(y/N): ")
        if confirm.lower() != 'y':
            print("操作已取消")
            return
    
    # 批量更新状态
    with transaction.atomic():
        updated_count = workflows.update(
            status='workflow_exception',
            finish_time=datetime.now()
        )
        print(f"✓ 已修复 {updated_count} 个工单")
        
        # 添加日志
        for workflow in workflows:
            try:
                audit_id = Audit.detail_by_workflow_id(
                    workflow_id=workflow.id, 
                    workflow_type=WorkflowType.SQL_REVIEW
                ).audit_id
                Audit.add_log(
                    audit_id=audit_id,
                    operation_type=6,
                    operation_type_desc="系统修复",
                    operation_info="工单执行超时，系统自动标记为异常状态",
                    operator="system",
                    operator_display="系统",
                )
            except Exception as e:
                print(f"添加日志失败 (工单ID: {workflow.id}): {e}")

def main():
    """主函数"""
    print("工单执行排队问题诊断和修复工具")
    print("=" * 50)
    
    # 检查问题
    stuck_queuing = check_queuing_workflows()
    stuck_executing = check_executing_workflows()
    check_django_q_tasks()
    
    # 提供修复选项
    if stuck_queuing or stuck_executing:
        print("\n发现卡住的工单，可以选择以下操作：")
        print("1. 修复排队中的工单（重置为审核通过）")
        print("2. 修复执行中的工单（标记为异常）")
        print("3. 修复特定工单ID")
        print("4. 退出")
        
        choice = input("\n请选择操作 (1-4): ")
        
        if choice == '1' and stuck_queuing:
            fix_stuck_queuing_workflows()
        elif choice == '2' and stuck_executing:
            fix_stuck_executing_workflows()
        elif choice == '3':
            workflow_ids = input("请输入工单ID（多个用逗号分隔）: ")
            try:
                ids = [int(id.strip()) for id in workflow_ids.split(',')]
                print("选择修复方式：")
                print("1. 重置为审核通过")
                print("2. 标记为异常")
                fix_choice = input("请选择 (1-2): ")
                
                if fix_choice == '1':
                    fix_stuck_queuing_workflows(workflow_ids=ids)
                elif fix_choice == '2':
                    fix_stuck_executing_workflows(workflow_ids=ids)
            except ValueError:
                print("工单ID格式错误")
        elif choice == '4':
            print("退出")
        else:
            print("无效选择或没有对应的工单需要修复")
    else:
        print("\n✓ 没有发现卡住的工单")

if __name__ == "__main__":
    main()
